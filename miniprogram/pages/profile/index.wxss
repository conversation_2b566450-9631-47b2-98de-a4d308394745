page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 个人页面样式 */
.profile-container {
  min-height: 100vh;
  background-color: #F5F7FB;
  padding: 48rpx;
}

/* 通用区域样式 */
.section {
  margin-bottom: 32rpx;
}

/* 用户信息区域 */
.section:first-child {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0;
}

.section:first-child:active {
  transform: scale(0.98);
}

.user-header {
  display: flex;
  align-items: center;
  position: relative;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 24rpx;
  overflow: hidden;
  background: #eaeaea;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 40rpx;
}

.avatar-placeholder {
  color: white;
  font-size: 32rpx;
  font-weight: 700;
  text-transform: uppercase;
}

.user-details {
  flex: 1;
}

.user-nickname {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.user-subtitle {
  font-size: 20rpx;
  color: #718096;
  font-weight: 700;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 签到按钮容器 */
.check-in-btn-container {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.check-in-btn {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  border-radius: 50rpx;
  padding: 12rpx 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(76, 81, 191, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.check-in-btn.loading {
  background: grey;
}

.check-in-btn:active {
  transform: scale(0.95);
}

.check-in-btn.checked {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
}

.check-in-btn .btn-text {
  font-size: 24rpx;
  font-weight: 600;
  color: white;
  white-space: nowrap;
}


/* 用户统计区域 */
.section:nth-child(2) {
  margin-bottom: 0rpx;
}

.stats-grid {
  display: flex;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  gap: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 35%;
  bottom: 35%;
  width: 0.5rpx;
  background: rgba(0, 0, 0, 0.1);
}

.stat-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.stat-label {
  font-size: 20rpx;
  color: #718096;
  font-weight: 400;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}



/* 会员信息区域 */
.membership-card {
  background: #1a1a1a;
  border-radius: 32rpx;
  padding: 32rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.membership-card:active {
  transform: scale(0.98);
}

.membership-card.vip {
  background: linear-gradient(135deg, #ffde7d 0%, #ffb347 100%);
}

.membership-card.vip .membership-title {
  color: #8b4513;
}

.membership-card.vip .membership-subtitle {
  color: rgba(139, 69, 19, 0.8);
}

.membership-card.vip .crown-icon {
  filter: drop-shadow(0 2rpx 4rpx rgba(139, 69, 19, 0.3));
}

.membership-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.35), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.membership-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.membership-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.crown-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(255, 215, 0, 0.3));
}

.membership-info {
  display: flex;
  flex-direction: column;
}

.membership-title {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 4rpx;
  letter-spacing: 1rpx;
}

.membership-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.membership-right {
  margin-left: 24rpx;
}

.membership-btn {
  background: white;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.membership-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
}

/* VIP记录模态框 */
.vip-records-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.vip-records-modal.show {
  opacity: 1;
  visibility: visible;
}

.vip-records-modal .modal-content {
  background: white;
  border-radius: 24rpx;
  width: 680rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.vip-records-modal.show .modal-content {
  transform: scale(1);
}

.records-stats {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16rpx;
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 8rpx;
}

.stats-value {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #2d3748;
}

.records-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.empty-records {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #718096;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 30rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.icon-text {
  font-size: 28rpx;
}

.record-content {
  flex: 1;
}

.record-title {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.record-details {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 4rpx;
}

.record-date {
  font-size: 24rpx;
  color: #718096;
}

.record-days {
  font-size: 24rpx;
  color: #28a745;
  font-weight: 600;
}

.record-source {
  margin-top: 4rpx;
}

.source-text {
  font-size: 22rpx;
  color: #4a5568;
}

.record-status {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.status-text {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 600;
}

.status-text.active {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-text.expired {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.load-more {
  text-align: center;
  padding: 32rpx 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more:active {
  opacity: 0.7;
}

.load-more-text {
  font-size: 28rpx;
  color: #553c9a;
  font-weight: 600;
}













.refresh-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.refresh-btn:active {
  opacity: 1;
  transform: rotate(180deg);
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.setting-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
}

.setting-item.danger {
  border-left: 4rpx solid #ef4444;
}

.setting-item.danger:active {
  background: rgba(239, 68, 68, 0.05);
}

.setting-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}

.setting-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(168, 237, 234, 0.2);
  border-radius: 50%;
}

.setting-item.danger .setting-icon {
  background: rgba(239, 68, 68, 0.1);
}

.setting-info {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.setting-item.danger .setting-title {
  color: #ef4444;
}

.setting-desc {
  display: block;
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.4;
}

.setting-arrow {
  font-size: 40rpx;
  color: #fff3f3;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.setting-switch {
  transform: scale(0.8);
}

.setting-item:active .setting-arrow {
  color: #553c9a;
  transform: translateX(8rpx);
}

.setting-item.danger:active .setting-arrow {
  color: #ef4444;
}

/* 设置项右侧内容 */
.setting-right {
  display: flex;
  align-items: center;
  min-width: 200rpx;
  justify-content: flex-end;
}

.picker-value {
  font-size: 28rpx;
  color: #553c9a;
  font-weight: 600;
  padding: 16rpx 24rpx;
  background: rgba(168, 237, 234, 0.2);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.picker-value:active {
  background: rgba(168, 237, 234, 0.3);
  border-color: #553c9a;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .setting-item {
    padding: 24rpx;
  }

  .setting-left {
    gap: 16rpx;
  }

  .setting-icon {
    font-size: 40rpx;
    width: 60rpx;
    height: 60rpx;
  }
}

.upgrade-item {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 179, 71, 0.1) 100%);
  border: 2rpx solid rgba(255, 215, 0, 0.3);
}

.upgrade-badge {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #8b4513;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}

.sync-status {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  min-width: 120rpx;
}

.sync-status.idle {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.sync-status.syncing {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 用户信息编辑模态框 */
.user-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-edit-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  width: 600rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.user-edit-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #718096;
  cursor: pointer;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: #f5f5f5;
}

.modal-body {
  padding: 40rpx 32rpx;
}

/* 模态框中的头像编辑区域 */
.modal-body .section:first-child {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.modal-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-avatar::after {
  border: none;
}

.modal-avatar:active {
  transform: scale(0.95);
}

.modal-avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 80rpx;
}

.modal-avatar-placeholder {
  color: white;
  font-size: 64rpx;
  font-weight: 700;
  text-transform: uppercase;
}

.modal-avatar-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 16rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.modal-avatar-text {
  font-size: 20rpx;
}

/* 模态框中的昵称编辑区域 */
.modal-body .section:last-child {
  margin-bottom: 0rpx;
}

.modal-nickname-input {
  width: auto;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #2d3748;
  transition: all 0.3s ease;
}

.modal-nickname-input:focus {
  border-color: #553c9a;
  background: white;
}

.modal-footer {
  display: flex;
  padding: 24rpx 32rpx 32rpx;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-btn.cancel {
  background: #f5f5f5;
  color: #4a5568;
}

.modal-btn.cancel:active {
  background: #e5e5e5;
}

.modal-btn.confirm {
  background: #553c9a;
  color: white;
}

.modal-btn.confirm:active {
  background: #4c51bf;
}